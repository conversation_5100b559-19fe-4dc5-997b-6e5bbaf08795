import { PlayerMetadata, RenderRequestPayload } from '@app/shared/types/ipc/mixcut'
import { Track, TrackType } from '@/modules/video-editor/types'
import { GeneratedMixcut } from '@/modules/mixcut/context/context'
import { byStartFrame, calculateRenderableOverlays } from '@/modules/video-editor/utils/overlay-helper'
import { removeAllLocalUrlOfOverlay } from '@/modules/video-editor/utils/track-helper'
import { Overlay, OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'

export function generateRenderRequestPayload(
  tracks: Track[],
  mixcut: GeneratedMixcut,
  playerMetadata: PlayerMetadata
): RenderRequestPayload {
  const overlays = calculateRenderableOverlays(removeAllLocalUrlOfOverlay(tracks), {
    [TrackType.VIDEO]: mixcut.videoCombo.selections,
    [TrackType.NARRATION]: mixcut.narrationSelections,
  }, true, false)

  return {
    id: __OVERLAY_RENDERER_VERSION__,
    inputProps: {
      overlays,
      playerMetadata
    }
  }
}

export function findFirstVideoOverlay(overlays: Overlay[]): VideoOverlay {
  return overlays
    .filter(o => o.type === OverlayType.VIDEO)
    .sort(byStartFrame())[0] as VideoOverlay
}

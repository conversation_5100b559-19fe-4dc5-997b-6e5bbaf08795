import { GeneratedMixcut, useMixcutContext } from '../context/context'
import { List, Play, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import React, { useMemo } from 'react'
import { clsx } from 'clsx'
import { OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { MultiSelectableCard } from './multi-selectable-card'
import { VideoPreviewFrame } from './video-preview-frame'
import { cn } from '@/components/lib/utils'

const GeneratedMixcutCard: React.FC<GeneratedMixcut & { index: number }> = ({ index, ...combo }) => {
  const { generation, state } = useMixcutContext()

  /**
   * @deprecated 使用 calculateRenderableOverlay + findFirstVideoOverlay
   */
  const firstVideoOverlay = useMemo(() => {
    const { tracks } = state
    // 获取第一个分镜对应的视频轨道索引
    const firstStoryboardTrackIndex = combo.videoCombo.selections[0]

    // 检查轨道索引是否有效
    if (firstStoryboardTrackIndex >= tracks.length) {
      console.warn(`视频轨道索引 ${firstStoryboardTrackIndex} 超出范围，总轨道数: ${tracks.length}`)
      return undefined
    }

    // 获取对应的视频轨道
    const targetVideoTrack = tracks[firstStoryboardTrackIndex]
    if (!targetVideoTrack || !targetVideoTrack.overlays.length) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 不存在或没有视频素材`)
      return undefined
    }

    // 获取轨道中第一个视频 overlay
    const firstVideoOverlay = targetVideoTrack.overlays.find(
      overlay => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
    ) as VideoOverlay | null

    if (!firstVideoOverlay || !firstVideoOverlay.src) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 中没有找到有效的视频素材`)
      return undefined
    }

    return firstVideoOverlay
  }, [state.tracks])

  return (
    <MultiSelectableCard {...generation} index={index}>
      <div
        style={{ aspectRatio: state.playerMetadata.width / state.playerMetadata.height }}
        className={clsx(
          'w-24 relative rounded-sm outline-3 cursor-pointer group',
        )}
      >
        {/* 预览图片背景 */}
        <VideoPreviewFrame overlay={firstVideoOverlay} />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(combo.videoCombo.similarity * 100).toFixed(1)}%
        </div>

        {/* 播放按钮 - 悬浮时显示在右下角 */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/70 hover:bg-black/90 border-0"
            onClick={e => {
              e.stopPropagation()
              generation.setActiveIndex(index)
            }}
          >
            <Play className="w-4 h-4 text-white" />
          </Button>
        </div>
      </div>
    </MultiSelectableCard>

  )
}

// 混剪预览列表
export const GeneratedMixcutListPanel = () => {
  const { generation: { setDrawerOpen, list, duplicateRateFilter } } = useMixcutContext()

  // 根据重复率筛选条件过滤混剪列表
  const filteredList = useMemo(() => {
    return list
      .filter(combo => {
        const similarityPercent = combo.videoCombo.similarity * 100

        return similarityPercent >= duplicateRateFilter.minRate
        && similarityPercent <= duplicateRateFilter.maxRate
      })
      .sort((a, b) => a.videoCombo.similarity - b.videoCombo.similarity)
  }, [list, duplicateRateFilter])

  if (filteredList.length) {
    return (
      <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
        {filteredList.map(combo => (
          <GeneratedMixcutCard
            {...combo}
            key={combo.videoCombo.selections.join(',')}
            index={list.indexOf(combo)} // 使用原始列表中的索引
          />
        ))}
      </div>
    )
  }

  // 判断是否是因为筛选导致的空结果
  const isFilteredEmpty = list.length > 0 && filteredList.length === 0

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 bg-background">
      {/* 空状态插图 */}
      <div className="mb-6">
        <div className="relative">
          {/* 简化的插图 - 人物和列表 */}
          <div className="w-32 h-32 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto mb-2 flex items-center justify-center">
                <List className="w-8 h-8 text-primary" />
              </div>
              <div className="space-y-1">
                <div className="w-12 h-1 bg-primary/30 rounded mx-auto" />
                <div className="w-8 h-1 bg-primary/20 rounded mx-auto" />
                <div className="w-10 h-1 bg-primary/20 rounded mx-auto" />
              </div>
            </div>
          </div>

          {/* 添加按钮 */}
          <div className="absolute -bottom-2 -right-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg">
              <Plus className="w-4 h-4 text-primary-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* 空状态文本 */}
      <div className="text-center mb-6">
        <p className="text-muted-foreground mb-2">
          {isFilteredEmpty
            ? `当前重复率筛选条件 (${duplicateRateFilter.minRate}%-${duplicateRateFilter.maxRate}%) 下没有匹配的混剪结果`
            : ''}
        </p>
      </div>

      {/* 添加视频按钮 */}
      <Button
        className={cn(isFilteredEmpty ? 'bg-primary hover:bg-primary/90' : 'bg-gradient-brand')}
        onClick={() => setDrawerOpen(true)}
      >
        {isFilteredEmpty ? '调整筛选条件' : '去生成混剪'}
      </Button>
    </div>
  )
}

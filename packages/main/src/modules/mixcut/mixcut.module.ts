import { Module } from '@nestjs/common'
import { MixcutService } from './mixcut.service.js'
import { MixcutIpcHandler } from './mixcut.ipc-handler.js'
import { FileUploaderModule } from '@/modules/file-uploader/file-uploader.module.js'
import { ResourceModule } from '@/modules/resource/resource.module.js'

@Module({
  imports: [
    FileUploaderModule,
    ResourceModule,
  ],
  providers: [
    MixcutService,
    MixcutIpcHandler,
  ],
})
export class MixcutModule {}
